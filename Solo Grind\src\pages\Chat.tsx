
import { useEffect, useState, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { BottomNav } from '@/components/BottomNav';
import { Loader2, Send, Users, Globe } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tables } from '@/integrations/supabase/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';

type MessageWithProfile = Tables<'messages'> & {
  profiles: Pick<Tables<'profiles'>, 'username' | 'avatar_url'> | null;
};

type GuildMessageWithProfile = Tables<'guild_messages'> & {
  profiles: Pick<Tables<'profiles'>, 'username' | 'avatar_url'> | null;
};

const fetchGlobalMessages = async () => {
  const { data, error } = await supabase
    .from('messages')
    .select('*, profiles(username, avatar_url)')
    .order('created_at', { ascending: true })
    .limit(100);

  if (error) {
    console.error('Error fetching global messages:', error);
    throw new Error(error.message);
  }
  return data as MessageWithProfile[];
};

const fetchGuildMessages = async (guildId: string) => {
  const { data, error } = await supabase
    .from('guild_messages')
    .select('*, profiles(username, avatar_url)')
    .eq('guild_id', guildId)
    .order('created_at', { ascending: true })
    .limit(100);

  if (error) {
    console.error('Error fetching guild messages:', error);
    throw new Error(error.message);
  }
  return data as GuildMessageWithProfile[];
};

function MessageItem({ message, isCurrentUser, isGuildMessage = false }: {
  message: MessageWithProfile | GuildMessageWithProfile;
  isCurrentUser: boolean;
  isGuildMessage?: boolean;
}) {
  const isSystemMessage = isGuildMessage && (message as GuildMessageWithProfile).message_type === 'system';
  const isAchievementMessage = isGuildMessage && (message as GuildMessageWithProfile).message_type === 'achievement';

  if (isSystemMessage) {
    return (
      <div className="flex justify-center my-2">
        <div className="bg-white/10 px-3 py-1 rounded-full">
          <p className="text-xs text-white/70">{message.content}</p>
        </div>
      </div>
    );
  }

  if (isAchievementMessage) {
    return (
      <div className="flex justify-center my-2">
        <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 px-3 py-2 rounded-lg">
          <p className="text-sm text-yellow-200">{message.content}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-start gap-3 ${isCurrentUser ? 'flex-row-reverse' : ''}`}>
      <Avatar className="h-8 w-8">
        <AvatarImage src={message.profiles?.avatar_url || undefined} />
        <AvatarFallback>{message.profiles?.username?.[0].toUpperCase() || 'U'}</AvatarFallback>
      </Avatar>
      <div className={`flex flex-col ${isCurrentUser ? 'items-end' : 'items-start'}`}>
        <div className={`p-3 rounded-lg max-w-xs md:max-w-md ${isCurrentUser ? 'bg-electric/80' : 'glass-card'}`}>
          <p className="text-sm text-white break-words">{message.content}</p>
        </div>
        <div className="text-2xs text-white/50 mt-1 px-1 flex items-center gap-1">
          {!isCurrentUser && <span className="font-semibold">{message.profiles?.username || 'User'}</span>}
          <span>{formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}</span>
          {isGuildMessage && (message as GuildMessageWithProfile).is_edited && (
            <span className="text-white/40">(edited)</span>
          )}
        </div>
      </div>
    </div>
  );
}

function MessageList({
  messages,
  userId,
  isGuildChat = false
}: {
  messages: (MessageWithProfile | GuildMessageWithProfile)[],
  userId: string | null,
  isGuildChat?: boolean
}) {
    const messagesEndRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    return (
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
            {messages.map((message) => (
                <MessageItem
                  key={message.id}
                  message={message}
                  isCurrentUser={message.user_id === userId}
                  isGuildMessage={isGuildChat}
                />
            ))}
            <div ref={messagesEndRef} />
        </div>
    );
}


function ChatInput({ isGuildChat, guildId }: { isGuildChat: boolean, guildId?: string }) {
    const [content, setContent] = useState('');
    const { user } = useAuth();

    const mutation = useMutation({
        mutationFn: async (newContent: string) => {
            if (!user) throw new Error('User not logged in');

            if (isGuildChat && guildId) {
                const { error } = await supabase.from('guild_messages').insert({
                    content: newContent,
                    user_id: user.id,
                    guild_id: guildId,
                });
                if (error) {
                    console.error("Error sending guild message:", error);
                    throw error;
                }
            } else {
                const { error } = await supabase.from('messages').insert({
                    content: newContent,
                    user_id: user.id,
                });
                if (error) {
                    console.error("Error sending global message:", error);
                    throw error;
                }
            }
        },
        onSuccess: () => {
             setContent('');
        },
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (content.trim()) {
            mutation.mutate(content.trim());
        }
    };

    const placeholder = isGuildChat ? "Message your guild..." : "Type a message...";

    return (
        <form onSubmit={handleSubmit} className="flex items-center p-4 bg-glass border-t border-white/10">
            <Input
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={placeholder}
                className="bg-transparent border-none focus-visible:ring-0 text-white placeholder:text-white/60"
                autoComplete="off"
                disabled={mutation.isPending}
            />
            <Button type="submit" size="icon" className="bg-electric rounded-full h-9 w-9 shrink-0" disabled={mutation.isPending || !content.trim()}>
                {mutation.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
            </Button>
        </form>
    );
}

export default function ChatPage() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState<'global' | 'guild'>('global');

  // Fetch user's guild membership
  const { data: guildMembership } = useQuery({
    queryKey: ['user-guild', user?.id],
    queryFn: async () => {
      if (!user) return null;
      const { data, error } = await supabase
        .from('guild_members')
        .select(`
          guild_id,
          guilds (
            name,
            icon_name
          )
        `)
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching guild membership:', error);
        return null;
      }
      return data;
    },
    enabled: !!user,
  });

  const { data: globalMessages, isLoading: isLoadingGlobal } = useQuery({
    queryKey: ['global-messages'],
    queryFn: fetchGlobalMessages,
    enabled: activeTab === 'global',
  });

  const { data: guildMessages, isLoading: isLoadingGuild } = useQuery({
    queryKey: ['guild-messages', guildMembership?.guild_id],
    queryFn: () => fetchGuildMessages(guildMembership!.guild_id),
    enabled: activeTab === 'guild' && !!guildMembership?.guild_id,
  });

  useEffect(() => {
    const globalChannel = supabase
      .channel('public:messages')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'messages' },
        (_payload) => {
           queryClient.invalidateQueries({ queryKey: ['global-messages'] });
        }
      )
      .subscribe();

    const guildChannel = supabase
      .channel('public:guild_messages')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'guild_messages' },
        (_payload) => {
           queryClient.invalidateQueries({ queryKey: ['guild-messages'] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(globalChannel);
      supabase.removeChannel(guildChannel);
    };
  }, [queryClient]);

  const isLoading = activeTab === 'global' ? isLoadingGlobal : isLoadingGuild;
  const messages = activeTab === 'global' ? globalMessages : guildMessages;

  return (
    <div className="pb-16 pt-6 min-h-screen bg-backdrop flex flex-col">
      <div className="px-4 pb-2 border-b border-white/10">
        <h1 className="gradient-title text-xl text-center font-bold mb-3">Chat</h1>

        {/* Chat Tabs */}
        <div className="flex gap-2">
          <button
            onClick={() => setActiveTab('global')}
            className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-lg font-semibold text-sm transition-all ${
              activeTab === 'global'
                ? 'bg-gradient-to-r from-electric to-purple text-backdrop'
                : 'glass-card text-white/70 hover:text-white'
            }`}
          >
            <Globe size={16} />
            Global
          </button>
          <button
            onClick={() => setActiveTab('guild')}
            disabled={!guildMembership}
            className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-lg font-semibold text-sm transition-all ${
              activeTab === 'guild'
                ? 'bg-gradient-to-r from-electric to-purple text-backdrop'
                : guildMembership
                  ? 'glass-card text-white/70 hover:text-white'
                  : 'glass-card text-white/40 cursor-not-allowed'
            }`}
          >
            <Users size={16} />
            {guildMembership ? guildMembership.guilds?.name || 'Guild' : 'No Guild'}
          </button>
        </div>
      </div>

      <div className="flex-1 flex flex-col overflow-y-hidden">
        {!guildMembership && activeTab === 'guild' ? (
          <div className="flex-1 flex items-center justify-center p-8">
            <div className="text-center">
              <Users className="h-12 w-12 text-white/40 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Join a Guild</h3>
              <p className="text-white/60 text-sm">You need to be in a guild to access guild chat.</p>
            </div>
          </div>
        ) : isLoading ? (
          <div className="flex-1 flex items-center justify-center">
            <Loader2 className="h-8 w-8 text-electric animate-spin" />
          </div>
        ) : (
          <MessageList
            messages={messages || []}
            userId={user?.id || null}
            isGuildChat={activeTab === 'guild'}
          />
        )}

        {(activeTab === 'global' || guildMembership) && (
          <ChatInput
            isGuildChat={activeTab === 'guild'}
            guildId={guildMembership?.guild_id}
          />
        )}
      </div>

      <BottomNav />
    </div>
  );
}
